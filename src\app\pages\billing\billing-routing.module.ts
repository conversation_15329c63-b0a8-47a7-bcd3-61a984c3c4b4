import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { MsalGuard } from '@azure/msal-angular';
import { AddPatientComponent } from '../common/add-patient.component';
import { SubmitMissingEncounterComponent } from '../common/submit-missing-encounter.component';
import { BillingComponent } from './billing.component';
import { EpicBillingExportComponent } from './epic-billing-export.component';


const routes: Routes = [
  { path: 'index', component: BillingComponent, canActivate: [MsalGuard] },
  { path: 'submit-missing-encounter', component: SubmitMissingEncounterComponent, canActivate: [MsalGuard] },
  { path: "add-patient", component: AddPatientComponent, canActivate: [MsalGuard] },
  { path: 'epic-billing-export', component:EpicBillingExportComponent,canActivate:[MsalGuard]}
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class BillingRoutingModule { }
