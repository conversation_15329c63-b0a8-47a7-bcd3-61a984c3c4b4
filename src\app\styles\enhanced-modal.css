/* Enhanced Modal Close Button Styles for iPhone and Safari Compatibility */

/* Enhanced close button styling for better touch interaction */
.enhanced-close-btn {
  position: relative;
  background: transparent !important;
  border: 0 !important;
  cursor: pointer !important;
  font-size: 1.5rem !important;
  font-weight: 700 !important;
  line-height: 1 !important;
  color: #000 !important;
  text-shadow: 0 1px 0 #fff !important;
  opacity: 0.5 !important;
  padding: 0.25rem 0.5rem !important;
  margin: -0.25rem -0.5rem -0.25rem auto !important;
  
  /* Enhanced touch target for mobile devices */
  min-width: 44px !important;
  min-height: 44px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  
  /* Prevent text selection */
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  user-select: none !important;
  
  /* Improve touch responsiveness */
  -webkit-tap-highlight-color: transparent !important;
  touch-action: manipulation !important;
}

.enhanced-close-btn:hover,
.enhanced-close-btn:focus {
  color: #000 !important;
  text-decoration: none !important;
  opacity: 0.75 !important;
  outline: none !important;
}

.enhanced-close-btn:not(:disabled):not(.disabled):hover,
.enhanced-close-btn:not(:disabled):not(.disabled):focus {
  opacity: 0.75 !important;
}

/* iPhone and Safari specific modal fixes */
@media (max-width: 767px) {
  /* Enhanced close button for mobile */
  .enhanced-close-btn {
    font-size: 1.75rem !important;
    min-width: 48px !important;
    min-height: 48px !important;
    padding: 0.5rem !important;
    margin: -0.5rem -0.5rem -0.5rem auto !important;
  }
  
  /* Modal header adjustments for mobile */
  .modal-header .enhanced-close-btn {
    color: #fff !important;
    text-shadow: none !important;
    opacity: 1 !important;
  }
  
  .modal-header .enhanced-close-btn:hover,
  .modal-header .enhanced-close-btn:focus {
    color: #fff !important;
    opacity: 0.8 !important;
  }
  
  /* Fix for iOS modal positioning */
  .modal.fade .modal-dialog {
    transform: none !important;
    transition: none !important;
  }
  
  .modal.show .modal-dialog {
    transform: none !important;
  }
  
  /* Fix for iOS modal backdrop */
  .modal-backdrop {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    background-color: rgba(0, 0, 0, 0.5) !important;
    z-index: 1040 !important;
  }

  /* Ensure modal content appears above backdrop */
  .modal {
    z-index: 1050 !important;
  }

  .modal-dialog {
    z-index: 1051 !important;
  }
  
  /* iOS specific modal body fixes */
  .modal-open {
    position: fixed !important;
    width: 100% !important;
    overflow: hidden !important;
  }
  
  /* Fix for iOS modal scrolling */
  .modal {
    -webkit-overflow-scrolling: touch !important;
    overflow-y: auto !important;
  }
  
  /* Prevent zoom on input focus for iOS */
  .modal input[type="text"],
  .modal input[type="email"],
  .modal input[type="password"],
  .modal input[type="number"],
  .modal textarea,
  .modal select {
    font-size: 16px !important;
  }
}

/* Safari desktop browser fixes */
@media (min-width: 768px) {
  /* Safari desktop modal backdrop fix */
  .modal-backdrop.show {
    opacity: 0.5 !important;
    z-index: 1040 !important;
  }

  /* Ensure modal content appears above backdrop on desktop */
  .modal {
    z-index: 1050 !important;
  }

  .modal-dialog {
    z-index: 1051 !important;
  }

  /* Ensure close button works properly in Safari */
  .enhanced-close-btn {
    cursor: pointer !important;
    background: transparent !important;
    border: 0 !important;
  }

  .enhanced-close-btn:focus {
    outline: none !important;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
  }
}

/* Additional iPhone specific fixes */
@supports (-webkit-touch-callout: none) {
  /* This targets iOS Safari specifically */
  .enhanced-close-btn {
    appearance: none !important;
    -webkit-touch-callout: none !important;
    /* Remove default button styling on iOS */
    background-image: none !important;
    border: none !important;
    border-radius: 0 !important;
    box-shadow: none !important;
  }

  .modal {
    -webkit-transform: translate3d(0, 0, 0) !important;
    transform: translate3d(0, 0, 0) !important;
  }

  .modal-dialog {
    -webkit-transform: translate3d(0, 0, 0) !important;
    transform: translate3d(0, 0, 0) !important;
  }
}

/* Force hardware acceleration for smoother animations */
.modal,
.modal-dialog,
.modal-content {
  -webkit-transform: translate3d(0, 0, 0) !important;
  transform: translate3d(0, 0, 0) !important;
  -webkit-backface-visibility: hidden !important;
  backface-visibility: hidden !important;
}

/* Accessibility improvements */
.enhanced-close-btn[aria-label] {
  position: relative;
}

.enhanced-close-btn[aria-label]:focus::after {
  content: attr(aria-label);
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  white-space: nowrap;
  z-index: 1000;
  pointer-events: none;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .enhanced-close-btn {
    border: 1px solid currentColor !important;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .modal.fade .modal-dialog {
    transition: none !important;
  }
  
  .modal-backdrop.fade {
    transition: none !important;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .enhanced-close-btn {
    color: #fff !important;
    text-shadow: 0 1px 0 #000 !important;
  }
  
  .enhanced-close-btn:hover,
  .enhanced-close-btn:focus {
    color: #fff !important;
  }
}
