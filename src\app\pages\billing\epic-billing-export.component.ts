import { DatePipe } from '@angular/common';
import { Component } from '@angular/core';
import { FormBuilder } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { AppComponent } from 'src/app/app.component';
import { BillingService } from 'src/app/services/billing/billing.service';
import { CommonService } from 'src/app/services/common/common.service';
import { EncrDecrServiceService } from 'src/app/services/common/encr-decr-service.service';

@Component({
  selector: 'app-epic',
  templateUrl: './epic-billing-export.component.html',
  styleUrls: ['./epic-billing-export.component.css']
})
export class EpicBillingExportComponent {

  public device = false;
  public timeout: any = null;
  public isClearData: boolean = false;
  constructor(private readonly billingServ: BillingService, private readonly commonServ: CommonService, private readonly encrDecr: EncrDecrServiceService
    , private readonly appComp: AppComponent, private readonly fb: FormBuilder, private readonly datePipe: DatePipe, private readonly toastr: ToastrService) {

  }

  ngOnInit() {
    this.device=this.appComp.device;
    this.appComp.loadPageName('Epic Billing Export', 'billingTab');
  }

}
