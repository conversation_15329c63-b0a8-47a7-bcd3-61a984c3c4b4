/* Message Hub Modal Styles */

/* Enhanced close button for better touch interaction */
.message-hub-modal .close {
    background: none !important;
    border: none !important;
    color: #fff !important;
    cursor: pointer !important;
    -webkit-tap-highlight-color: transparent !important;
    touch-action: manipulation !important;
    -webkit-user-select: none !important;
    user-select: none !important;
    min-width: 44px !important;
    min-height: 44px !important;
    padding: 10px !important;
    z-index: 1050 !important;
}

.message-hub-modal .close:hover,
.message-hub-modal .close:focus {
    color: #fff !important;
    opacity: 0.8 !important;
    text-decoration: none !important;
}

/* Add iOS-specific modal fixes */
.message-hub-modal {
    -webkit-overflow-scrolling: touch !important;
    transform: translateZ(0);
}

/* Enhanced modal backdrop for iOS */
.modal-backdrop {
    -webkit-tap-highlight-color: transparent !important;
    touch-action: manipulation !important;
}

/* Mobile modal fixes */
@media only screen and (max-width: 767.98px) {
    /* Mobile modal positioning */
    .mobile-cpt-modal {
        max-width: 95% !important;
        margin: 0 auto;
    }

    .mobile-cpt-modal .modal-content {
        border-radius: 8px;
        overflow: hidden;
    }

    /* Enhanced close button for mobile */
    .mobile-cpt-modal .close {
        padding: 0.75rem !important;
        min-width: 48px !important;
        min-height: 48px !important;
    }
}

/* Safari specific modal fixes */
@supports (-webkit-appearance: none) {
    /* Safari close button fixes */
    .close {
        -webkit-appearance: none !important;
        appearance: none !important;
    }

    /* Safari desktop modal fixes */
    @media only screen and (min-width: 768px) {
        .message-hub-modal .close:hover,
        .message-hub-modal .close:focus {
            opacity: 0.8 !important;
            outline: none !important;
        }
    }

    /* Safari modal positioning */
    .message-hub-modal {
        -webkit-backface-visibility: hidden !important;
        backface-visibility: hidden !important;
    }

    /* Safari body scroll fix */
    .modal-open {
        position: fixed;
        width: 100%;
    }
}

/* iPhone landscape orientation fix */
@media only screen and (max-width: 767.98px) and (orientation: landscape) {
    .message-hub-modal .modal-dialog {
        max-height: 80vh;
        overflow-y: auto;
    }
}