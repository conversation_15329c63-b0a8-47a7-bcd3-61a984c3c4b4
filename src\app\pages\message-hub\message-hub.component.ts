import { Component, OnInit } from '@angular/core';
import { MessageHubService } from 'src/app/services/message-hub/message-hub.service';
import { AppComponent } from 'src/app/app.component';
import { CommonService } from 'src/app/services/common/common.service';
import { EncrDecrServiceService } from 'src/app/services/common/encr-decr-service.service';
import { ToastrService } from 'ngx-toastr';
declare let $: any;

@Component({
  selector: 'app-message-hub',
  templateUrl: './message-hub.component.html',
  styleUrls: ['./message-hub.component.css']
})
export class MessageHubComponent implements OnInit {
  public listOfMessages: Array<any> = [];
  public listOfFacilities: Array<any> = [];
  public p: number;
  public searchByName: string;
  public request: any = {};
  public device: boolean = false;
  public navigator = navigator;
  constructor(private readonly mgsServ: MessageHubService, private readonly appComp: AppComponent, private readonly commonServ: CommonService
    , private readonly encrDecr: EncrDecrServiceService, private readonly toastr: ToastrService) { }

  ngOnInit() {
    this.device = this.appComp.device;
    this.appComp.loadPageName('Message Hub', 'messageHubTab');
    this.getMessages();
  }

  // Simplified device detection
  isIPhone(): boolean {
    return /iPhone|iPad|iPod/i.test(navigator.userAgent);
  }

  isSafari(): boolean {
    const userAgent = navigator.userAgent;
    return /Safari/i.test(userAgent) && !/Chrome/i.test(userAgent) && !/Chromium/i.test(userAgent);
  }

  isIPhoneOrSafari(): boolean {
    return this.isIPhone() || this.isSafari();
  }

  getMessages() {
    this.commonServ.startLoading();
    this.mgsServ.getMessages().subscribe((p: any) => {
      this.listOfMessages = p;
      this.commonServ.stopLoading();
    }, error => { this.commonServ.stopLoading(); });
  }

  markAllAsRead() {
    this.commonServ.startLoading();
    this.request.sGROUP_ID = this.encrDecr.set('0');
    this.request.FLAG = this.encrDecr.set('ALL');
    this.mgsServ.markAsAllRead(this.request).subscribe((p: any) => {
      this.request = {};
      this.mgsServ.getMessages().subscribe((p: any) => {
        this.listOfMessages = p;
      }, error => { });
      this.commonServ.stopLoading();
    }, error => {
      this.request = {};
      this.commonServ.stopLoading();
    });
  }

  markAsRead(groupId, isRead) {
    if (!isRead) {
      this.request.sGROUP_ID = this.encrDecr.set(groupId);
      this.request.FLAG = this.encrDecr.set('BADGE');
      this.mgsServ.markAsAllRead(this.request).subscribe((p: any) => {
        this.request = {};
      }, error => {
        this.request = {};
      });
    }

    const modalId = "#readMsg-" + groupId;
    $(modalId).modal('show');
  }

  archiveMessage(groupId) {
    this.commonServ.startLoading();
    this.request.sGROUPID = this.encrDecr.set(groupId.toString());
    this.mgsServ.archiveMessage(this.request).subscribe((p: any) => {
      this.request = {};
      if (p > 0) {
        this.mgsServ.getMessages().subscribe((p: any) => {
          this.listOfMessages = p;
        }, error => { });
        this.commonServ.stopLoading();
      }
    }, error => {
      this.request = {};
      this.commonServ.stopLoading();
    });
  }

  closeReadMgs(id, isRead, event?: Event) {
    const modalId = "#readMsg-" + id;
    
    // Prevent default only for Safari
    if (event && this.isIPhoneOrSafari()) {
      event.preventDefault();
    }
    
    $(modalId).modal('hide');
    
    // Handle message refresh
    if (!isRead) {
      this.mgsServ.getMessages().subscribe((p: any) => {
        this.listOfMessages = p;
      });
    }
  }

  openNotes() {
    this.commonServ.startLoading();
    this.commonServ.getFacilities().subscribe((p: any) => {
      this.listOfFacilities = p;
      $('#sendMessagePop').modal('show');
      this.commonServ.stopLoading();
    }, error => { console.error(error.status); });
  }
}